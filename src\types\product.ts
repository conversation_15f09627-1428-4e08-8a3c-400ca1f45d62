import type { ProductGroup } from './product-group'
import type { Supplier } from './supplier'
import type { SpecialReportGroup } from './special-report-group'

export interface Product {
  id: number
  product_code: string
  product_name: string
  generic_name: string
  standard_cost: number
  selling_price: number
  storage_location: string
  stock_min: number
  stock_max: number
  packing_size: string
  reg_no: string
  manufacturer: Supplier
  distributor: Supplier
  indications: string
  warnings: string
  purchase_notes: string
  cost_notes: string
  sales_alert_message: string
  generic_group: string
  product_group: ProductGroup
  wholesale_control_group: string
  special_report_group: SpecialReportGroup
  unit: string
  barcode: string
  isactive: boolean
  wholesale: boolean
  wholesale1: number
  wholesale2: number
  wholesale3: number
  wholesale4: number
  wholesale5: number
  retail: boolean
  retail1: number
  retail2: number
  retail3: number
  retail4: number
  retail5: number
}
