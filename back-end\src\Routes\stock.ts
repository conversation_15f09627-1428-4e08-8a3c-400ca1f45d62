import { Router } from "express";
import { StockController } from "../Controllers/stock";

const router = Router();
const stockController = new StockController();

router.get("/", stockController.getAll); // GET Stock ทั้งหมด
router.get("/:id", stockController.getById); // GET Stock ตาม ID
router.post("/", stockController.getAllByFilter.bind(stockController));
router.get("/branch/:branchId", stockController.getByBranchId);
router.put("/:id", stockController.update); // UPDATE Stock
router.delete("/:id", stockController.delete); // DELETE Stock

export default router;
