import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login',
  },
  {
    path: '/home',
    component: () => import('layouts/MainLayout.vue'),
    children: [{ path: '', component: () => import('pages/IndexPage.vue') }],
    meta: { public: true },
  },
  {
    path: '/login',
    component: () => import('layouts/EmptyLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('src/pages/LoginPage.vue'),
      },
    ],
    meta: { public: true },
  },
  {
    path: '/dashboard',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '1',
        name: 'dashboard-1',
        component: () => import('src/pages/Dashboard/DashBoardPage.vue'),
      },
      {
        path: 'chart',
        name: 'dashboard-chart',
        component: () => import('src/pages/Dashboard/DashBoardPage2.vue'),
      },
    ],
    meta: { public: true },
  },
  {
    path: '/stock',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'product',
        name: 'product-list',
        component: () => import('src/pages/Stock/ProductPage.vue'),
      },
      {
        path: 'order',
        name: 'order-list',
        component: () => import('src/pages/Stock/OrderPage.vue'),
      },
      {
        path: 'receive',
        name: 'goods-receive',
        component: () => import('src/pages/Stock/GoodReceivePage.vue'),
      },
      {
        path: 'status',
        name: 'status-order',
        component: () => import('src/pages/Stock/StatusOrderPage.vue'),
      },
      {
        path: 'inventory',
        name: 'inventory',
        component: () => import('src/pages/Stock/InventoryPage.vue'),
      },
    ],
    meta: { public: true },
  },
  {
    path: '/suppliers',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'supplier-list',
        component: () => import('src/pages/Supplier/SupplierPage.vue'),
      },
    ],
  },
  {
    path: '/user',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'user-dashboard',
        component: () => import('src/pages/User/userDashBoard.vue'),
      },
      {
        path: 'summary',
        name: 'user-summary',
        component: () => import('src/pages/User/userSummarypage.vue'),
      },
      {
        path: 'management',
        name: 'user-management',
        component: () => import('src/pages/User/userManagement.vue'),
      },
      {
        path: 'checkinout',
        name: 'user-checkinout',
        component: () => import('src/pages/User/checkInOut.vue'),
      },
      {
        path: 'attendance-table',
        name: 'attendance-table',
        component: () => import('src/pages/User/userAttendancetable.vue'),
      },
      {
        path: 'attendance/:id',
        name: 'user-attendance-detail',
        component: () => import('src/pages/User/userAttendanceDetail.vue'),
      },
      {
        path: 'userDetail/:id',
        name: 'user-detail',
        component: () => import('src/pages/User/userDetail.vue'),
      },
      {
        path: 'leaveRequest/:id',
        name: 'user-leave-request',
        component: () => import('src/pages/User/userLeaveRequest.vue'),
      }
    ],
    meta: { public: true },
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
]

export default routes
