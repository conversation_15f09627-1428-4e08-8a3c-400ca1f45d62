<!--
  WorkHoursSection Component
  Displays today's work hours information
-->
<template>
  <div class="work-hours-section">
    <div class="work-hours-title">{{ props.title }}</div>

    <div class="work-hours-container">
      <!-- Total Hours -->
      <div class="work-hours-item">
        <div class="hours-label">{{ props.hoursLabel }}</div>
        <div class="hours-value" :class="getHoursValueClass(props.totalHours)">
          {{ formatHoursDisplay(props.totalHours) }}
        </div>
      </div>

      <!-- Check In Time -->
      <div class="work-hours-item">
        <div class="hours-label">{{ props.checkInLabel }}</div>
        <div class="hours-value" :class="{ 'time-recorded': props.checkInTime !== '-' }">
          {{ props.checkInTime }}
        </div>
      </div>

      <!-- Check Out Time -->
      <div class="work-hours-item">
        <div class="hours-label">{{ props.checkOutLabel }}</div>
        <div class="hours-value" :class="{ 'time-recorded': props.checkOutTime !== '-' }">
          {{ props.checkOutTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  totalHours: string | number
  checkInTime: string
  checkOutTime: string
  title?: string
  hoursLabel?: string
  checkInLabel?: string
  checkOutLabel?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'ชั่วโมงทำงานวันนี้',
  hoursLabel: 'จำนวนชั่วโมง',
  checkInLabel: 'เข้า',
  checkOutLabel: 'ออก',
})

/**
 * Format hours display with unit
 */
const formatHoursDisplay = (hours: string | number): string => {
  if (hours === '-' || hours === 0) return '-'
  return `${hours} ชั่วโมง`
}

/**
 * Get CSS class for hours value based on status
 */
const getHoursValueClass = (hours: string | number): string => {
  if (hours === '-') return 'hours-pending'
  if (typeof hours === 'number' && hours > 0) return 'hours-completed'
  return ''
}
</script>

<style scoped>
.work-hours-section {
  background: rgba(145, 210, 193, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.work-hours-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.work-hours-container {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.work-hours-item {
  flex: 1;
  text-align: center;
  background: rgba(145, 210, 193, 0.5);
  padding: 12px 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.work-hours-item:hover {
  background: rgba(145, 210, 193, 0.6);
}

.hours-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.2;
}

.hours-value {
  font-size: 0.95rem;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.hours-value.time-recorded {
  color: #2e7d32;
}

.hours-value.hours-completed {
  color: #1976d2;
}

.hours-value.hours-pending {
  color: #757575;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .work-hours-container {
    flex-direction: column;
    gap: 8px;
  }

  .work-hours-item {
    padding: 10px;
  }

  .hours-label {
    font-size: 0.8rem;
  }

  .hours-value {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .work-hours-section {
    padding: 12px;
  }

  .work-hours-title {
    font-size: 0.9rem;
    margin-bottom: 10px;
  }

  .work-hours-item {
    padding: 8px;
  }

  .hours-label {
    font-size: 0.75rem;
  }

  .hours-value {
    font-size: 0.85rem;
  }
}
</style>
