import { Router } from "express";
import { BranchController } from "../Controllers/branch";

const router = Router();
const branchController = new BranchController();

router.get("/", branchController.getAll);
router.get("/:id", branchController.getById);
router.post("/", branchController.create);
router.put("/:id", branchController.update);
router.delete("/:id", branchController.delete);

export default router;
