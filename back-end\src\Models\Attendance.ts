import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from "typeorm";
import { User } from "./User";

@Entity()
export class Attendance {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'date' })
  date!: string;

  @Column({ type: 'time' })
  clock_in!: string;

  @Column({ type: 'time', nullable: true })
  clock_out?: string;

  @Column({ type: 'float', nullable: true })
  work_duration?: number;

  @Column({ nullable: true })
  note?: string;

  @Column({ default: 'Present' })
  status!: string;

  @ManyToOne(() => User, user => user.attendances)
  user!: User;

  @CreateDateColumn()
  created_at!: Date;
}
