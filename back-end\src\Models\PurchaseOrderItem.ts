import { <PERSON>tity, PrimaryGeneratedColumn, ManyToOne, Column } from "typeorm";
import { PurchaseOrder } from "./PurchaseOrder";
import { Product } from "./Product";

@Entity()
export class PurchaseOrderItem {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => PurchaseOrder, (purchaseOrder) => purchaseOrder.purchase_order_items, { nullable: false })
  purchase_order!: PurchaseOrder;

  @ManyToOne(() => Product, { nullable: false })
  product!: Product;

  @Column({ type: "integer"})
  quantity!: number;

  @Column({ type: "numeric"})
  unit_price!: number;

  @Column({ type: "numeric"})
  total_price!: number;
}