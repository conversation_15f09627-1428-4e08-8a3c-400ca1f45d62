<template>
  <div>
    <!-- ไอคอนกระดิ่ง -->
    <q-icon name="notifications" size="32px" class="cursor-pointer" @click="showDialog = true">
      <q-badge v-if="lowStockProducts.length > 0" color="red" floating rounded>
        {{ lowStockProducts.length }}
      </q-badge>
    </q-icon>

    <!-- Dialog แจ้งเตือน -->
    <q-dialog v-model="showDialog">
      <q-card class="card-container">
        <q-card-section class="card-header">
          <div class="row justify-between">
            <div style="font-size: 18px" class="flex flex-center">
              <q-icon name="notifications" size="32px" class="q-mr-sm" />การแจ้งเตือน
            </div>
            <q-btn icon="close" v-close-popup flat rounded />
          </div>
        </q-card-section>

        <q-card-section class="card-body">
          <div v-if="lowStockProducts.length === 0" class="text-center">
            ไม่มีสินค้าที่ใกล้หมดหรือหมดแล้ว
          </div>
          <div v-else>
            <q-list class="q-gutter-md">
              <q-card
                v-for="product in lowStockProducts"
                :key="product.id"
                :class="product.remaining === 0 ? 'bg-red text-black' : 'bg-orange'"
                class="q-pa-md"
              >
                <q-item>
                  <q-item-section avatar>
                    <q-icon
                      :name="product.remaining === 0 ? 'warning' : 'priority_high'"
                      :color="product.remaining === 0 ? 'black' : 'black'"
                      size="md"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-bold">
                      สินค้า {{ product.product.product_name }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-badge
                      :color="product.remaining === 0 ? 'red' : 'orange'"
                      class="text-black text-bold"
                      style="font-size: 14px"
                    >
                      {{ product.remaining === 0 ? 'หมดแล้ว' : 'ใกล้หมด' }}
                    </q-badge>
                  </q-item-section>
                </q-item>
              </q-card>
            </q-list>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useStockStore } from 'src/stores/stock'

const showDialog = ref(false)
const stockStore = useStockStore()

// คำนวณหาสินค้าที่ใกล้หมดหรือหมดแล้ว
const lowStockProducts = computed(() => {
  return stockStore.stocks.filter(
    (stock) => stock.remaining === 0 || stock.remaining < stock.product.stock_min,
  )
})

// ดึงข้อมูล Stock เมื่อ Component ถูกโหลด
onMounted(async () => {
  await stockStore.fetchAllStock()
})
</script>

<style scoped>
.card-header {
  background-color: #91d2c1;
  color: black;
}

.card-body {
  background-color: #e1edea;
}

.card-container {
  width: 100%;
  max-width: 500px;
}
</style>
