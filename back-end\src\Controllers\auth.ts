import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { User } from "../Models/User";
import * as bcrypt from "bcrypt"; // <-- เพิ่ม

export class AuthController {
  public async login(req: Request, res: Response): Promise<void> {
    const { name, password } = req.body;

    try {
      const userRepository = AppDataSource.getRepository(User);

      // ค้นหาผู้ใช้จากชื่อ
      const user = await userRepository.findOne({
        where: { name },
        relations: ["branch"],
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      // เปรียบเทียบรหัสผ่านโดยใช้ bcrypt
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        res.status(400).json({ message: "Invalid credentials" });
        return;
      }

      // ถ้ารหัสผ่านตรง ส่งข้อมูลผู้ใช้กลับ โดยไม่รวม password
      const { password: _, ...userWithoutPassword } = user;
      res.status(200).json(userWithoutPassword);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
}