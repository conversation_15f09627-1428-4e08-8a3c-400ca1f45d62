import { Router } from "express";
import { UserController } from "../Controllers/User";

const router = Router();
const controller = new UserController();

router.get("/", controller.getAll.bind(controller));
router.get("/count", controller.getUserCount.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));
router.get("/images", controller.getUserImages.bind(controller));
router.get("/verify/images", controller.verifyUserImages.bind(controller));
router.get("/image/:id", controller.getUserImageById.bind(controller));

export default router;
