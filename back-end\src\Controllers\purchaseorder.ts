import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { Supplier } from "../Models/Supplier";
import { User } from "../Models/User";
import { Product } from "../Models/Product";

export class PurchaseOrderController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
      const purchaseOrders = await purchaseOrderRepository.find({
        relations: ["supplier", "user", "purchase_order_items", "purchase_order_items.product"]
      });
      res.status(200).json(purchaseOrders);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
      const purchaseOrder = await purchaseOrderRepository.findOne({
        where: { id: Number(id) },
        relations: ["supplier", "user", "purchase_order_items", "purchase_order_items.product"]
      });
      if (purchaseOrder) {
        res.status(200).json(purchaseOrder);
      } else {
        res.status(404).json({ message: "Purchase Order not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getPurchaseOrderItemsByPoId(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
      const purchaseOrderItem = await purchaseOrderItemRepository.find({
        where: { purchase_order: { id: Number(id) } },
        relations: [
          "product",
          "product.distributor",
          "product.manufacturer",
          "product.product_group",
          "product.special_report_group"
        ]
      });
      if (purchaseOrderItem) {
        res.status(200).json(purchaseOrderItem);
      } else {
        res.status(404).json({ message: "Purchase Order Item not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  // public async getPurchaseOrderItemsByPoId(req: Request, res: Response): Promise<void> {
  //   const { id } = req.params;
  //   try {
  //     const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
  //     const purchaseOrderItem = await purchaseOrderRepository.findOne({
  //       where: { id: Number(id) },
  //       relations: ["purchase_order_items", "purchase_order_items.product"]
  //     });
  //     if (purchaseOrderItem) {
  //       res.status(200).json(purchaseOrderItem);
  //     } else {
  //       res.status(404).json({ message: "Purchase Order Item not found" });
  //     }
  //   } catch (error) {
  //     res.status(500).json({ message: "Server error", error });
  //   }
  // }

  public async create(req: Request, res: Response): Promise<void> {
    const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
    const supplierRepository = AppDataSource.getRepository(Supplier);
    const userRepository = AppDataSource.getRepository(User);
    const productRepository = AppDataSource.getRepository(Product);
    const {
      supplier,
      user,
      contact,
      address,
      date,
      po_total,
      tax,
      tax_total,
      status,
      order_date,
      order_discount,
      note,
      order_total,
      receive_total,
      product_price_tax,
      order_discount_tax,
      receive_status,
      purchase_order_items
    } = req.body;

    // ดึง id จาก object
    const supplierId = supplier?.id;
    const userId = user?.id;

    // แปลง order_date เป็น YYYYMMDD
    const formattedDate = new Date(order_date).toISOString().slice(0, 10).replace(/-/g, "");

    // หา ID ล่าสุด
    const lastPurchaseOrder = await purchaseOrderRepository.find({
      order: { id: "DESC" },
      take: 1
    });

    const lastId = lastPurchaseOrder.length > 0 ? lastPurchaseOrder[0].id + 1 : 1;

    // สร้าง code ใหม่
    const generatedCode = `PO-${formattedDate}-${lastId}`;
    if (!supplierId || !userId) {
      res.status(400).json({ message: "Supplier or User ID is missing" });
      return;
    }

    // หา Supplier และ User ใน Database
    const [existingSupplier, existingUser] = await Promise.all([
      supplierRepository.findOne({ where: { id: supplierId } }),
      userRepository.findOne({ where: { id: userId } })
    ]);

    if (!existingSupplier || !existingUser) {
      res.status(400).json({
        message: "Validation Error",
        errors: {
          supplier: existingSupplier ? null : "Supplier not found",
          user: existingUser ? null : "User not found"
        }
      });
      return;
    }

    // สร้าง Purchase Order
    const newPurchaseOrder = purchaseOrderRepository.create({
      code: generatedCode,
      supplier: existingSupplier,
      contact,
      address,
      date,
      user: existingUser,
      po_total,
      tax,
      tax_total,
      status,
      order_date,
      order_discount,
      note,
      order_total,
      receive_total,
      product_price_tax,
      order_discount_tax,
      receive_status,
    });

    const savedPurchaseOrder = await purchaseOrderRepository.save(newPurchaseOrder);

    // บันทึก Purchase Order Items
    if (purchase_order_items && purchase_order_items.length > 0) {
      const items = await Promise.all(
        purchase_order_items.map(async (item: any) => {
          const productId = item.product?.id;
          if (!productId) {
            throw new Error("Product ID is missing");
          }
          const product = await productRepository.findOne({ where: { id: productId } });
          if (!product) {
            throw new Error(`Product with id ${productId} not found`);
          }
          return purchaseOrderItemRepository.create({
            purchase_order: savedPurchaseOrder,
            product: product,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.quantity * item.unit_price,
          });
        })
      );
      await purchaseOrderItemRepository.save(items);
    }

    res.status(201).json({
      id: savedPurchaseOrder.id,
      code: savedPurchaseOrder.code,
      address: savedPurchaseOrder.address,
      contact: savedPurchaseOrder.contact,
      date: savedPurchaseOrder.date,
      note: savedPurchaseOrder.note,
      order_date: savedPurchaseOrder.order_date,
      order_discount: savedPurchaseOrder.order_discount,
      order_discount_tax: savedPurchaseOrder.order_discount_tax,
      order_total: savedPurchaseOrder.order_total,
      po_total: savedPurchaseOrder.po_total,
      product_price_tax: savedPurchaseOrder.product_price_tax,
      receive_status: savedPurchaseOrder.receive_status,
      receive_total: savedPurchaseOrder.receive_total,
      status: savedPurchaseOrder.status,
      supplier: savedPurchaseOrder.supplier,
      tax: savedPurchaseOrder.tax,
      tax_total: savedPurchaseOrder.tax_total,
      user: {
        id: savedPurchaseOrder.user.id,
        name: savedPurchaseOrder.user.name
        // **อย่าคืนค่ารหัสผ่านเพื่อความปลอดภัย**
      }
    });

  }


  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
    try {
      const purchaseOrder = await purchaseOrderRepository.findOne({ where: { id: Number(id) } });
      if (purchaseOrder) {
        purchaseOrderRepository.merge(purchaseOrder, req.body);
        const updatedPurchaseOrder = await purchaseOrderRepository.save(purchaseOrder);
        res.status(200).json(updatedPurchaseOrder);
      } else {
        res.status(404).json({ message: "Purchase Order not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Purchase Order", error });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
    try {
      const result = await purchaseOrderRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Purchase Order not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}