<template>
  <div class="calendar-container">
    <div class="row justify-between items-center q-mb-sm">
      <div class="text-subtitle1">{{ title }}</div>
      <q-btn flat round size="sm" icon="refresh" @click="resetDateRange" />
    </div>
    <q-date
      v-model="dateRange"
      class="full-width no-shadow faded-other-months"
      minimal
      range
      @range-start="onRangeStart"
      @range-end="onRangeEnd"
      :options="dateOptions"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'
import { date } from 'quasar'

const props = defineProps({
  title: {
    type: String,
    default: 'ช่วง',
  },
  modelValue: {
    type: Object,
    default: () => ({
      from: date.formatDate(new Date(), 'YYYY-MM-DD'),
      to: date.formatDate(new Date(), 'YYYY-MM-DD'),
    }),
  },
})

const emit = defineEmits(['update:modelValue', 'reset', 'range-complete'])

// Date range picker ref
const dateRange = ref(props.modelValue)

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    dateRange.value = newValue
  },
  { deep: true },
)

// Watch for internal changes to dateRange
watch(
  dateRange,
  (newValue) => {
    console.log('Date range changed:', newValue)
    emit('update:modelValue', newValue)
  },
  { deep: true },
)

// Store the start date when range selection begins
const rangeStartDate = ref('')

// Handle range start selection
const onRangeStart = (from: { year: number; month: number; day: number }) => {
  // Format with padded month and day for consistent YYYY-MM-DD format
  rangeStartDate.value = `${from.year}-${String(from.month).padStart(2, '0')}-${String(from.day).padStart(2, '0')}`
  console.log('Range start selected:', rangeStartDate.value)
}

// Handle range end selection with one-week limit
const onRangeEnd = (range: {
  from: { year: number; month: number; day: number }
  to: { year: number; month: number; day: number }
}) => {
  // Format dates consistently as YYYY-MM-DD
  const fromFormatted = `${range.from.year}-${String(range.from.month).padStart(2, '0')}-${String(range.from.day).padStart(2, '0')}`
  const toFormatted = `${range.to.year}-${String(range.to.month).padStart(2, '0')}-${String(range.to.day).padStart(2, '0')}`

  const startDate = new Date(fromFormatted)
  const endDate = new Date(toFormatted)

  console.log('Range end selected:', {
    from: fromFormatted,
    to: toFormatted,
  })

  // Calculate the difference in days
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  console.log('Difference in days:', diffDays)

  // If more than 7 days, limit to 7 days from start
  if (diffDays > 7) {
    const maxEndDate = new Date(startDate)
    maxEndDate.setDate(startDate.getDate() + 6) // 7 days including start date

    // Update the model with the limited range
    dateRange.value = {
      from: fromFormatted,
      to: date.formatDate(maxEndDate, 'YYYY-MM-DD'),
    }
  } else {
    // Ensure consistent format in the model
    dateRange.value = {
      from: fromFormatted,
      to: toFormatted,
    }
  }

  // Emit range-complete event when selection is finished
  emit('range-complete', dateRange.value)
}

// Date options to limit selection to 7 days
const dateOptions = (date: string) => {
  if (!rangeStartDate.value) return true

  const start = new Date(rangeStartDate.value)
  const current = new Date(date)
  const diffTime = Math.abs(current.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays <= 7
}

// Reset date range to current date
const resetDateRange = () => {
  const today = date.formatDate(new Date(), 'YYYY-MM-DD')
  dateRange.value = {
    from: today,
    to: today,
  }
  rangeStartDate.value = ''

  console.log('Date range reset to:', dateRange.value)

  // Emit reset event
  emit('reset')
}
</script>

<style scoped>
.calendar-container {
  margin-bottom: 16px;
  margin-left: 16px;
  margin-right: 16px;
}

.no-shadow :deep(.q-date) {
  box-shadow: none;
}

/* Fade out dates from other months in QDate component */
.faded-other-months :deep(.q-date__calendar-item--out) {
  opacity: 0.3;
}

.faded-other-months :deep(.q-date__calendar-item--out .q-btn) {
  color: #999 !important;
}

.faded-other-months :deep(.q-date__calendar-item--out:hover .q-btn) {
  background-color: rgba(0, 0, 0, 0.05) !important;
}
</style>
