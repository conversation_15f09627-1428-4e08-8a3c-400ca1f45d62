<template>
  <div class="row container-search">
    <span style="color: white">ข้อความ</span>
    <q-input
      v-model="searchTerm"
      :placeholder="placeholder"
      borderless
      dense
      class="input-container-search"
    >
    </q-input>

    <q-icon name="search" style="margin-left: 20px; font-size: xx-large; color: gainsboro" />
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue'

const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: 'ค้นหา',
  },
})

const emit = defineEmits(['update:modelValue'])
const searchTerm = ref(props.modelValue)

watch(searchTerm, (newValue) => {
  emit('update:modelValue', newValue)
})
</script>

<style scoped>
.search-box {
  background-color: #c3e7dd;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: 100%;
}

.container-search {
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #294888;
  width: 400px;
  height: 50px;
}

.input-container-search {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-left: 20px;
  border-radius: 5px;
  height: 35px;
  width: 250px;
}
</style>
