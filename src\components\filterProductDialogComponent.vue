<template>
  <div class="filter-box">
    <q-select
      v-model="selectedFilter"
      :options="filterOptions"
      label="ตัวกรอง"
      borderless
      dense
      options-dense
      behavior="menu"
      emit-value
      map-options
      popopup-content-class="custom-dropdown"
      class="custom-select"
    >
      <template v-slot:selected>
        <span class="selected-text">{{ selectedFilterLabel }}</span>
      </template>
      <template v-slot:option="scope">
        <q-item clickable v-ripple @click="toggleFilter(scope.opt.value)">
          <q-item-section avatar>
            <q-radio v-model="selectedFilter" :val="scope.opt.value" color="black" />
          </q-item-section>
          <q-item-section>
            {{ scope.opt.label }}
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed, watch } from 'vue'

interface FilterOption {
  label: string
  value: string
}

const props = defineProps({
  modelValue: String,
  filterOptions: {
    type: Array as () => FilterOption[],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue'])

// แก้ไข: ให้ selectedFilter เป็น string เสมอ
const selectedFilter = ref<string>(props.modelValue || '')

const toggleFilter = (value: string) => {
  selectedFilter.value = selectedFilter.value === value ? '' : value
  emit('update:modelValue', selectedFilter.value)
}

// อัปเดตค่า selectedFilter ถ้า modelValue เปลี่ยน
watch(
  () => props.modelValue,
  (newValue) => {
    selectedFilter.value = newValue || ''
  },
)

const selectedFilterLabel = computed(() => {
  return props.filterOptions.find((opt) => opt.value === selectedFilter.value)?.label || ''
})
</script>

<style scoped>
.filter-box {
  background-color: #294888;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: 150px;
  height: 50px;
  margin-left: 20px;
  margin-top: 10px;
}

.custom-dropdown .q-item {
  background-color: #deecff;
  color: black;
}

.custom-select :deep(.q-icon) {
  color: white;
}

.custom-select :deep(.q-field__label) {
  color: white;
}

.custom-select :deep(.q-field__control) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* เปลี่ยนสีตัวอักษรเมื่อเลือกแล้ว */
.selected-text {
  color: white;
  text-align: center;

  margin-left: 5px;
}
</style>
