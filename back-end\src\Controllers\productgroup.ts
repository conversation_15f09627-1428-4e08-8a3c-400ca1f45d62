import { Request, Response } from "express";
import { AppDataSource } from "../Config/db"; 
import { ProductGroup } from "../Models/ProductGroup"; 

export class ProductGroupController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const productGroupRepository = AppDataSource.getRepository(ProductGroup);
      const productGroups = await productGroupRepository.find();
      res.status(200).json(productGroups);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const productGroupRepository = AppDataSource.getRepository(ProductGroup);
      const productGroup = await productGroupRepository.findOne({ where: { id: Number(id) } });
      if (productGroup) {
        res.status(200).json(productGroup);
      } else {
        res.status(404).json({ message: "ProductGroup not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const newProductGroup = AppDataSource.getRepository(ProductGroup).create(req.body);
    try {
      const savedProductGroup = await AppDataSource.getRepository(ProductGroup).save(newProductGroup);
      res.status(201).json(savedProductGroup);
    } catch (error) {
      res.status(400).json({ message: "Error creating ProductGroup" });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const productGroupRepository = AppDataSource.getRepository(ProductGroup);
    try {
      const productGroup = await productGroupRepository.findOne({ where: { id: Number(id) } });
      if (productGroup) {
        productGroupRepository.merge(productGroup, req.body);
        const updatedProductGroup = await productGroupRepository.save(productGroup);
        res.status(200).json(updatedProductGroup);
      } else {
        res.status(404).json({ message: "ProductGroup not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating ProductGroup" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const productGroupRepository = AppDataSource.getRepository(ProductGroup);
    try {
      const result = await productGroupRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "ProductGroup not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
}
