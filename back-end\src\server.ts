import express, { Request, Response } from "express";
import bodyParser from "body-parser";
import cors from "cors";
import { AppDataSource } from "./Config/db"; // เชื่อมต่อกับฐานข้อมูล
import supplierTypeRouter from "./Routes/suppliertype"; // นำเข้า router ของ SupplierType
import supplierRouter from "./Routes/suppllier"; // นำเข้า router ของ SupplierType
import productGroupRouter from "./Routes/productgroup";
import productRouter from "./Routes/product";
import stockRoutes from "./Routes/stock";
import branchRoutes from "./Routes/branch";
import userRoutes from "./Routes/user";
import PurchaseOrderRoutes from "./Routes/purchaseorder";
import PurchaseOrderItemRoutes from "./Routes/purchaseorderitem";
import SpecialReportGroupRoutes from "./Routes/specialreportgroup";
import authRoutes from "./Routes/auth";
import attendanceRoutes from "./Routes/attendance";
import path from "path";
import leaveRequestRoutes from "./Routes/leaveRequest";

const app = express();
const PORT = process.env.PORT || 3000;

// ตั้งค่า middleware
app.use(cors()); // เปิดใช้งาน CORS
app.use(bodyParser.json()); // แปลง JSON request body เป็น object

// Serve static files from the images directory
app.use('/images', express.static(path.join(__dirname, '../images')));

// Add this to debug the path
console.log("Images directory path:", path.join(__dirname, '../images'));

// เชื่อมต่อกับฐานข้อมูล
AppDataSource.initialize()
  .then(() => {
    console.log("✅ Database connected successfully");
    
    // ตั้งค่า routes
    app.use("/suppliertypes", supplierTypeRouter);
    app.use("/supplier", supplierRouter);
    app.use("/productgroup", productGroupRouter);
    app.use("/product", productRouter);
    app.use("/stock", stockRoutes);
    app.use("/branch", branchRoutes);
    app.use("/user", userRoutes);
    app.use("/purchaseorder", PurchaseOrderRoutes);
    app.use("/purchaseorderitem", PurchaseOrderItemRoutes);
    app.use("/specialreportgroup", SpecialReportGroupRoutes);
    app.use("/auth", authRoutes);
    app.use("/attendance", attendanceRoutes);
    app.use("/leave-request", leaveRequestRoutes);

    // ตั้งค่า endpoint ราก
    app.get("/", (req: Request, res: Response) => {
      res.send("Welcome to the Supplier Type API!");
    });

    // เ่่มเซิร์ฟเวอร์
    app.listen(PORT, () => {
      console.log(`🚀 Server is running on http://localhost:${PORT}`);
    });
  })
  .catch((error) => {
    console.error("❌ Error during Data Source initialization:", error);
  });
