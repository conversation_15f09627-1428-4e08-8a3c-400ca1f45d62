<!--
  WeekDaysDisplay Component
  Displays week days with today highlighted
-->
<template>
  <div class="week-days">
    <div
      v-for="(day, index) in props.weekDays"
      :key="`${day.letter}-${day.number}-${index}`"
      class="day-item"
      :class="{ 'active-day': day.isToday }"
      :title="getDayTitle(day, index)"
    >
      <div class="day-letter">{{ day.letter }}</div>
      <div class="day-number">{{ day.number }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WeekDay } from '../types'

interface Props {
  weekDays: WeekDay[]
}

const props = defineProps<Props>()

/**
 * Get title for day item (for accessibility)
 */
const getDayTitle = (day: WeekDay, index: number): string => {
  const today = new Date()
  const dayOffset = index - 3
  const targetDate = new Date(today)
  targetDate.setDate(today.getDate() + dayOffset)

  const dayName = targetDate.toLocaleDateString('th-TH', { weekday: 'long' })
  const dateString = targetDate.toLocaleDateString('th-TH')

  return day.isToday ? `วันนี้ - ${dayName} ${dateString}` : `${dayName} ${dateString}`
}
</script>

<style scoped>
.week-days {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 16px;
}

.day-item {
  flex: 1;
  text-align: center;
  padding: 12px 8px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  cursor: default;
  min-width: 0; /* Prevent flex items from overflowing */
}

.day-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.day-item.active-day {
  background: #91d2c1;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(145, 210, 193, 0.3);
}

.day-item.active-day:hover {
  transform: scale(1.05) translateY(-2px);
}

.day-letter {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1;
}

.day-number {
  font-size: 1.1rem;
  font-weight: bold;
  line-height: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .week-days {
    gap: 6px;
  }

  .day-item {
    padding: 10px 6px;
  }

  .day-letter {
    font-size: 0.8rem;
  }

  .day-number {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .week-days {
    gap: 4px;
  }

  .day-item {
    padding: 8px 4px;
    border-radius: 8px;
  }

  .day-letter {
    font-size: 0.75rem;
    margin-bottom: 2px;
  }

  .day-number {
    font-size: 0.9rem;
  }
}
</style>
