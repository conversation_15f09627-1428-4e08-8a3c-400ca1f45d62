<!--
  CheckInOutButtons Component
  Handles check-in and check-out actions
-->
<template>
  <div class="action-buttons">
    <!-- Check Out Button -->
    <q-btn
      flat
      class="check-out-btn"
      :disable="!canCheckOut || loading"
      :loading="loading && actionType === 'checkout'"
      @click="handleCheckOut"
    >
      <template v-if="!loading || actionType !== 'checkout'" #default>
        <q-icon name="logout" class="q-mr-sm" />
        {{ checkOutLabel }}
      </template>
    </q-btn>

    <!-- Check In Button -->
    <q-btn
      class="check-in-btn bg-green"
      :disable="!canCheckIn || loading"
      :loading="loading && actionType === 'checkin'"
      @click="handleCheckIn"
    >
      <template v-if="!loading || actionType !== 'checkin'" #default>
        <q-icon name="login" class="q-mr-sm" />
        {{ checkInLabel }}
      </template>
    </q-btn>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  isCheckedIn: boolean
  loading?: boolean
  checkInLabel?: string
  checkOutLabel?: string
}

interface Emits {
  (e: 'checkIn'): void
  (e: 'checkOut'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  checkInLabel: 'Check In',
  checkOutLabel: 'Check Out',
})

const emit = defineEmits<Emits>()

// Local state for tracking action type
const actionType = ref<'checkin' | 'checkout' | null>(null)

/**
 * Check if user can check in
 */
const canCheckIn = computed(() => !props.isCheckedIn)

/**
 * Check if user can check out
 */
const canCheckOut = computed(() => props.isCheckedIn)

/**
 * Handle check-in action
 */
const handleCheckIn = () => {
  if (!canCheckIn.value || props.loading) return

  actionType.value = 'checkin'
  try {
    emit('checkIn')
  } finally {
    // Reset action type after a delay to allow for loading state
    setTimeout(() => {
      actionType.value = null
    }, 1000)
  }
}

/**
 * Handle check-out action
 */
const handleCheckOut = () => {
  if (!canCheckOut.value || props.loading) return

  actionType.value = 'checkout'
  try {
    emit('checkOut')
  } finally {
    // Reset action type after a delay to allow for loading state
    setTimeout(() => {
      actionType.value = null
    }, 1000)
  }
}
</script>

<script lang="ts">
import { computed } from 'vue'
</script>

<style scoped>
.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.check-out-btn,
.check-in-btn {
  flex: 1;
  border-radius: 10px;
  font-weight: bold;
  padding: 12px;
  font-size: 1rem;
  min-height: 48px;
  transition: all 0.3s ease;
}

.check-out-btn {
  background: #9e9e9e;
  color: white;
}

.check-out-btn:not(:disabled):hover {
  background: #757575;
  transform: translateY(-2px);
}

.check-out-btn:disabled {
  background: #e0e0e0;
  color: #bdbdbd;
}

.check-in-btn {
  color: white;
}

.check-in-btn:not(:disabled):hover {
  background: #388e3c;
  transform: translateY(-2px);
}

.check-in-btn:disabled {
  background: #e0e0e0;
  color: #bdbdbd;
}

/* Loading state styles */
.check-out-btn.q-btn--loading,
.check-in-btn.q-btn--loading {
  transform: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .check-out-btn,
  .check-in-btn {
    font-size: 0.9rem;
    padding: 10px;
    min-height: 44px;
  }
}

@media (max-width: 480px) {
  .action-buttons {
    gap: 8px;
  }

  .check-out-btn,
  .check-in-btn {
    font-size: 0.85rem;
    padding: 8px;
    min-height: 40px;
  }
}
</style>
