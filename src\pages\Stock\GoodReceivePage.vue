<template>
  <StockNavigation></StockNavigation>
  <div class="container q-pa-md q-ma-md">
    <div class="row q-col-gutter-md wrap">
      <div class="col-sm-12 col-md-8" style="max-width: 1042px; width: 100%">
        <SearchComponent placeholder="ค้นหา" />
      </div>
      <div class="col-sm-6 col-md-2">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" />
      </div>
    </div>
    <q-card flat class="custom-table"> </q-card>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import StockNavigation from 'src/components/StockNavigation.vue'
import SearchComponent from 'src/components/searchComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'

// const pagination = ref({
//   rowsPerPage: 12,
// })

onMounted(async () => {})

// const columns = [
//   {
//     name: 'id',
//     label: 'รหัสคำสั่งซื้อ',
//     field: 'id',
//     align: 'left' as const,
//     sortable: true,
//   },
//   {
//     name: 'product_code',
//     label: 'วันที่สั่งซื้อ',
//     field: 'product_code',
//     align: 'left' as const,
//     sortable: true,
//   },
//   {
//     name: 'product_name',
//     label: 'จำนวนสินค้า',
//     field: 'product_name',
//     align: 'left' as const,
//     sortable: true,
//   },
//   {
//     name: 'unit',
//     label: 'ชื่อผู้สั่ง',
//     field: 'unit',
//     align: 'center' as const,
//     sortable: true,
//   },
//   {
//     name: 'wholesale_prices.wholesale1',
//     label: 'สถานะ',
//     field: 'wholesale1',
//     align: 'center' as const,
//     sortable: true,
//   },
// ]

const selectedFilter = ref<string>('')

const filterOptions = [
  { label: 'บาร์โค้ด', value: 'barcode' },
  { label: 'รหัสสินค้า', value: 'product_code' },
  { label: 'ชื่อสินค้า', value: 'product_name' },
  { label: 'สถานที่เก็บ', value: 'storage_location' },
  { label: 'กลุ่มชื่อสามัญ', value: 'common_name_group' },
  { label: 'ข้อความเตือน', value: 'warning_message' },
]
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}
</style>
