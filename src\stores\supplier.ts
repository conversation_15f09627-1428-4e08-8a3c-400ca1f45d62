import { defineStore } from 'pinia'
import { supplierService } from 'src/services/supplierService' // Import supplierService
import type { Supplier } from 'src/types/supplier'

const defaultForm: Supplier = {
  id: 0,
  supplier_number: '',
  name: '',
  address: '',
  tel: '',
  tax_number: '',
  fax: '',
  contact_name: '',
  email: '',
  type: {
    id: 0,
    name: '',
  },
  isactive: false,
}
function getSupplierPayload(form: Supplier): Supplier {
  return {
    ...form,
    type:
      form.type && form.type.id
        ? { id: form.type.id, name: form.type.name || '' }
        : { id: 0, name: '' },
  }
}

export const useSupplierStore = defineStore('supplier', {
  state: () => ({
    form: { ...defaultForm },
    suppliers: [] as Supplier[], // Store suppliers list
  }),

  actions: {
    async fetchSupplier() {
      try {
        this.suppliers = await supplierService.getSuppliers()
      } catch (error) {
        console.error('Error fetching products:', error)
      }
    },
    async loadSuppliers() {
      try {
        const suppliers = await supplierService.getSuppliers()
        this.suppliers = suppliers
      } catch (error) {
        console.error('Error loading suppliers:', error)
      }
    },

    async deleteSupplier(id: number) {
      try {
        await supplierService.deleteSupplier(id)
        this.suppliers = this.suppliers.filter((supplier) => supplier.id !== id) // Update state
      } catch (error) {
        console.error('Error deleting supplier:', error)
      }
    },

    async addSupplier(supplier: Supplier) {
      try {
        const maxId = this.suppliers.length > 0 ? Math.max(...this.suppliers.map((p) => p.id)) : 0
        this.form.id = maxId + 1
        const payload = getSupplierPayload(supplier) // Create payload
        const newSupplier = await supplierService.addSupplier(payload) // Add supplier using payload
        this.suppliers.push(newSupplier) // Add the new supplier to state
      } catch (error) {
        console.error('Error adding supplier:', error)
      }
    },

    async updateSupplier(supplier: Supplier) {
      try {
        const payload = getSupplierPayload(supplier)
        console.log('Payload being sent:', payload)
        const updatedSupplier = await supplierService.updateSupplier(payload)
        const index = this.suppliers.findIndex((s) => s.id === updatedSupplier.id)
        if (index !== -1) {
          this.suppliers[index] = updatedSupplier
        }
      } catch (error) {
        console.error('Error updating supplier:', error)
      }
    },
    async filterSuppliers(search: string, filter: string) {
      try {
        this.suppliers = await supplierService.getSuppliersByFilter(search, filter)
      } catch (error) {
        console.error('Error filtering suppliers:', error)
      }
    },

    resetForm() {
      this.form = JSON.parse(JSON.stringify(defaultForm))
    },
  },
})
