import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { LeaveRequest } from "../Models/LeaveRequest";
import { User } from "../Models/User";

export class LeaveRequestController {
  public async create(req: Request, res: Response): Promise<void> {
    const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
    const userRepository = AppDataSource.getRepository(User);

    try {
      const { userId, leaveDate, leaveType, reason } = req.body;

      if (!userId || !leaveDate || !leaveType) {
        res
          .status(400)
          .json({ message: "userId, leaveDate, and leaveType are required" });
        return;
      }

      // Validate leave type
      if (leaveType !== "sick" && leaveType !== "personal") {
        res
          .status(400)
          .json({ message: "leaveType must be either 'sick' or 'personal'" });
        return;
      }

      // Find the user
      const user = await userRepository.findOne({
        where: { id: Number(userId) },
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      // Check if the date is in the future
      const currentDate = new Date().toISOString().split("T")[0];
      if (leaveDate < currentDate) {
        res.status(400).json({ message: "Leave date must be in the future" });
        return;
      }

      // Check if the date is the user's day off
      const leaveDay = new Date(leaveDate).getDay();
      const dayNames = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];

      if (user.day_off === dayNames[leaveDay]) {
        res
          .status(400)
          .json({ message: "Cannot request leave on your weekly day off" });
        return;
      }

      // Check if there's already a leave request for this date
      const existingLeave = await leaveRequestRepository.findOne({
        where: {
          user: { id: userId },
          leave_date: leaveDate,
        },
      });

      if (existingLeave) {
        res
          .status(400)
          .json({ message: "You already have a leave for this date" });
        return;
      }

      // Check if user has sufficient leave balance
      if (leaveType === "sick") {
        if ((user.sick_leave_remaining || 0) < 1) {
          res.status(400).json({
            message: "Insufficient sick leave balance",
            remaining: user.sick_leave_remaining || 0,
          });
          return;
        }
      } else if (leaveType === "personal") {
        if ((user.personal_leave_remaining || 0) < 1) {
          res.status(400).json({
            message: "Insufficient personal leave balance",
            remaining: user.personal_leave_remaining || 0,
          });
          return;
        }
      }

      // Create the leave request
      const newLeaveRequest = leaveRequestRepository.create({
        user,
        leave_date: leaveDate,
        leave_type: leaveType,
        reason,
      });

      const savedLeaveRequest = await leaveRequestRepository.save(
        newLeaveRequest
      );

      // Update the user's leave count and deduct from remaining balance
      if (leaveType === "sick") {
        user.sick_leave = (user.sick_leave || 0) + 1;
        user.sick_leave_remaining = (user.sick_leave_remaining || 0) - 1;
      } else if (leaveType === "personal") {
        user.personal_leave = (user.personal_leave || 0) + 1;
        user.personal_leave_remaining =
          (user.personal_leave_remaining || 0) - 1;
      }

      await userRepository.save(user);

      res.status(201).json({
        id: savedLeaveRequest.id,
        leave_date: savedLeaveRequest.leave_date,
        leave_type: savedLeaveRequest.leave_type,
        user: {
          id: user.id,
          name: user.name,
          sick_leave: user.sick_leave,
          personal_leave: user.personal_leave,
          sick_leave_remaining: user.sick_leave_remaining,
          personal_leave_remaining: user.personal_leave_remaining,
        },
      });
    } catch (error) {
      console.error("Leave request error:", error);
      res.status(500).json({ message: "Server error during leave request" });
    }
  }

  public async getUserLeaveRequests(
    req: Request,
    res: Response
  ): Promise<void> {
    const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);

    try {
      const userId = Number(req.params.userId);

      if (!userId) {
        res.status(400).json({ message: "userId is required" });
        return;
      }

      const leaveRequests = await leaveRequestRepository.find({
        where: { user: { id: userId } },
        relations: ["user"],
        order: {
          leave_date: "DESC",
        },
      });

      // Clean up response data
      const cleanedLeaveRequests = leaveRequests.map((leaveRequest) => {
        const { user, ...leaveRequestData } = leaveRequest;
        return {
          ...leaveRequestData,
          user: {
            id: user.id,
            name: user.name,
            sick_leave: user.sick_leave,
            personal_leave: user.personal_leave,
            sick_leave_remaining: user.sick_leave_remaining,
            personal_leave_remaining: user.personal_leave_remaining,
          },
        };
      });

      res.status(200).json(cleanedLeaveRequests);
    } catch (error) {
      console.error("Get leave requests error:", error);
      res
        .status(500)
        .json({ message: "Server error while fetching leave requests" });
    }
  }

  public async updateDayOff(req: Request, res: Response): Promise<void> {
    const userRepository = AppDataSource.getRepository(User);

    try {
      const { userId, dayOff } = req.body;

      if (!userId || !dayOff) {
        res.status(400).json({ message: "userId and dayOff are required" });
        return;
      }

      // Validate day off
      const validDays = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ];
      if (!validDays.includes(dayOff)) {
        res
          .status(400)
          .json({ message: "dayOff must be a valid day of the week" });
        return;
      }

      // Find the user
      const user = await userRepository.findOne({
        where: { id: Number(userId) },
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      // Update the user's day off
      user.day_off = dayOff;
      await userRepository.save(user);

      res.status(200).json({
        id: user.id,
        name: user.name,
        day_off: user.day_off,
      });
    } catch (error) {
      console.error("Update day off error:", error);
      res.status(500).json({ message: "Server error during day off update" });
    }
  }

  public async getAllLeaveRequests(req: Request, res: Response): Promise<void> {
    const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);

    try {
      const leaveRequests = await leaveRequestRepository.find({
        relations: ["user"],
        order: {
          leave_date: "DESC",
        },
      });

      // Clean up response data
      const cleanedLeaveRequests = leaveRequests.map((leaveRequest) => {
        const { user, ...leaveRequestData } = leaveRequest;
        return {
          ...leaveRequestData,
          user: {
            id: user.id,
            name: user.name,
            sick_leave: user.sick_leave,
            personal_leave: user.personal_leave,
            sick_leave_remaining: user.sick_leave_remaining,
            personal_leave_remaining: user.personal_leave_remaining,
          },
        };
      });

      res.status(200).json(cleanedLeaveRequests);
    } catch (error) {
      console.error("Get all leave requests error:", error);
      res
        .status(500)
        .json({ message: "Server error while fetching all leave requests" });
    }
  }
}
